// 查询参数
export const defaultQuery = {
    account: '',
    strategyDate: '',
    dateRange: ['', ''],
    strategyType: 1,
    reportType: 1
};

// 默认配置数据
export const defaultSdSpotConfig = {
    account: '',
    strategyDate: '',
    time: '',
    strategyType: 1,
    reportType: 1,
    power: 0,
    reportPower: 0,
    status: 0,
    createTime: '',
    updateTime: ''
};

// 山东现货时间段配置（24小时，每小时一个时段）
export const timeOptions = Array.from({ length: 24 }, (_, index) => {
    const hour = index.toString().padStart(2, '0');
    return {
        label: `${hour}:00`,
        value: `${hour}:00`
    };
});

// 申报类型选项
export const strategyTypeOptions = [
    { label: '类型1', value: 1 },
    { label: '类型2', value: 2 },
    { label: '日内', value: 3 },
    { label: '日前', value: 4 }
];

// 申报方式选项
export const reportTypeOptions = [
    { label: '手动', value: 1 },
    { label: '自动', value: 2 }
];

// 状态选项
export const statusOptions = [
    { label: '未申报', value: 0 },
    { label: '成功', value: 1 },
    { label: '失败', value: 2 }
];

// 状态文本映射
export const statusTextMap = {
    0: '未申报',
    1: '成功',
    2: '失败'
};

// 申报类型文本映射
export const strategyTypeTextMap = {
    1: '类型1',
    2: '类型2',
    3: '日内',
    4: '日前'
};

// 申报方式文本映射
export const reportTypeTextMap = {
    1: '手动',
    2: '自动'
};
