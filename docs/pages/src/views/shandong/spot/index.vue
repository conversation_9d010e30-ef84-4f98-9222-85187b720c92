<template>
    <div class="sd-spot-wrapper">
        <!-- 查询条件 -->
        <el-card class="header" size="small">
            <el-form :model="query" inline label-position="left" size="small">
                <el-form-item label="账户">
                    <el-select v-model="query.account" placeholder="请选择账户" style="width: 200px">
                        <el-option v-for="item in accountList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="策略日期">
                    <el-date-picker
                        v-model="query.strategyDate"
                        type="date"
                        placeholder="选择日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 140px"
                    />
                </el-form-item>
                <el-form-item label="日期范围">
                    <el-date-picker
                        v-model="query.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 240px"
                    />
                </el-form-item>
                <el-form-item label="申报类型">
                    <el-select v-model="query.strategyType" style="width: 120px">
                        <el-option v-for="item in strategyTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申报方式">
                    <el-select v-model="query.reportType" style="width: 120px">
                        <el-option v-for="item in reportTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="updateData">查询</el-button>
                    <el-button @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 操作按钮 -->
        <div class="actions">
            <el-button type="success" @click="saveData" :disabled="!configData.length"> 保存配置 </el-button>
            <el-button type="warning" @click="exportData"> 导出数据 </el-button>
            <el-button type="info" @click="importData"> 导入数据 </el-button>
        </div>

        <!-- 主要内容区域 -->
        <el-card class="content" size="small">
            <div class="table-container">
                <!-- 配置表格 -->
                <div class="config-section">
                    <h3>申报配置</h3>
                    <config-table ref="configTableRef" v-model="configData" :query="query" :height="tableHeight" />
                </div>

                <!-- 历史记录表格 -->
                <div class="record-section">
                    <record-table ref="recordTableRef" :query="query" :height="tableHeight" @edit="handleEditRecord" @delete="handleDeleteRecord" />
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup>
    import { computed, nextTick, onMounted, ref } from 'vue';
    import { ElMessage } from 'element-plus';
    import dayjs from 'dayjs';
    import ConfigTable from './components/ConfigTable.vue';
    import RecordTable from './components/RecordTable.vue';
    import { defaultQuery, reportTypeOptions, strategyTypeOptions } from './types.js';
    import { queryConfigList, saveConfigList } from '@/api/sdSpot';
    import { useCommonStore } from '@/stores/common';

    const commonStore = useCommonStore();

    // 响应式数据
    const query = ref({
        ...defaultQuery,
        strategyDate: dayjs().format('YYYY-MM-DD'),
        dateRange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    });

    const configData = ref([]);
    const configTableRef = ref();
    const recordTableRef = ref();

    // 模拟账户列表（实际项目中应该从API获取）
    const accountList = ref([
        { label: '账户1', value: 'account1' },
        { label: '账户2', value: 'account2' },
        { label: '账户3', value: 'account3' }
    ]);

    // 计算表格高度
    const tableHeight = computed(() => {
        return Math.max(400, window.innerHeight - 300);
    });

    // 保存配置数据
    const saveData = async () => {
        if (!configData.value.length) {
            ElMessage.warning('没有配置数据');
            return;
        }

        if (!query.value.account) {
            ElMessage.warning('请选择账户');
            return;
        }

        if (!query.value.strategyDate) {
            ElMessage.warning('请选择策略日期');
            return;
        }

        try {
            const params = {
                account: query.value.account,
                strategyDate: query.value.strategyDate,
                configList: configData.value.map((item) => ({
                    account: item.account,
                    strategyDate: item.strategyDate,
                    time: item.time,
                    strategyType: item.strategyType,
                    reportType: item.reportType,
                    power: item.power
                }))
            };

            const response = await saveConfigList(params);
            if (response.code === 1) {
                ElMessage.success('保存成功');
                updateData();
            }
        } catch (error) {
            console.error('保存失败:', error);
            ElMessage.error('保存失败');
        }
    };

    // 更新数据
    const updateData = async () => {
        if (!query.value.account || !query.value.strategyDate) {
            return;
        }

        try {
            const params = {
                account: query.value.account,
                strategyDate: query.value.strategyDate
            };

            const response = await queryConfigList(params);
            if (response.code === 1 && response.data?.length > 0) {
                configData.value = response.data.map((item, index) => ({
                    ...item,
                    key: index,
                    timeIndex: index,
                    disabled: item.status === 1 // 已成功申报的不允许修改
                }));
            } else {
                configData.value = [];
            }

            // 刷新历史记录表格
            await nextTick();
            recordTableRef.value?.refreshData();
        } catch (error) {
            console.error('查询失败:', error);
            ElMessage.error('查询失败');
        }
    };

    // 重置查询条件
    const resetQuery = () => {
        query.value = {
            ...defaultQuery,
            strategyDate: dayjs().format('YYYY-MM-DD'),
            dateRange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        };
        configData.value = [];
    };

    // 处理编辑记录
    const handleEditRecord = (record) => {
        // 将记录数据加载到配置表格中进行编辑
        configData.value = [record].map((item, index) => ({
            ...item,
            key: index,
            timeIndex: index,
            disabled: false
        }));

        // 更新查询条件
        query.value.account = record.account;
        query.value.strategyDate = record.strategyDate;

        ElMessage.info('记录已加载到配置表格，可进行编辑');
    };

    // 处理删除记录
    const handleDeleteRecord = (record) => {
        ElMessage.success('记录删除成功');
        updateData();
    };

    // 导出数据
    const exportData = () => {
        ElMessage.info('导出功能待实现');
    };

    // 导入数据
    const importData = () => {
        ElMessage.info('导入功能待实现');
    };

    // 组件挂载时初始化
    onMounted(() => {
        // 设置默认账户
        if (accountList.value.length > 0) {
            query.value.account = accountList.value[0].value;
        }
    });
</script>

<style scoped lang="scss">
    .sd-spot-wrapper {
        padding: 16px;
        height: 100vh;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .header {
            flex-shrink: 0;
        }

        .actions {
            display: flex;
            gap: 10px;
            flex-shrink: 0;
        }

        .content {
            flex: 1;
            overflow: hidden;

            .table-container {
                height: 100%;
                display: flex;
                gap: 16px;

                .config-section {
                    flex: 1;
                    display: flex;
                    flex-direction: column;

                    h3 {
                        margin: 0 0 10px 0;
                        font-size: 16px;
                        font-weight: 600;
                    }
                }

                .record-section {
                    flex: 1;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1200px) {
        .sd-spot-wrapper {
            .content {
                .table-container {
                    flex-direction: column;

                    .config-section,
                    .record-section {
                        flex: none;
                        height: 50%;
                    }
                }
            }
        }
    }
</style>
